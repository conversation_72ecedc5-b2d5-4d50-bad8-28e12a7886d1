package main

import (
	"fmt"
	"log"

	"github.com/real-rm/goresodownload"
)

// Example demonstrating the optimized downloader usage
func main() {
	// Create optimized configuration
	config := goresodownload.NewDefaultConfig()
	
	// Performance optimizations are now enabled by default:
	// - DisableSync: true (no fsync for better performance)
	// - Directory creation optimization (MkdirAll only once per unique directory)
	
	fmt.Printf("Default config optimizations:\n")
	fmt.Printf("- DisableSync: %v (better performance, acceptable risk for media files)\n", config.DisableSync)
	fmt.Printf("- Directory creation: optimized (MkdirAll only once per unique directory)\n")
	
	// If you need data safety over performance, you can re-enable sync:
	// config.DisableSync = false
	
	// Create downloader options
	opts := &goresodownload.DownloaderOptions{
		Config:       config,
		StoragePaths: []string{"/path/to/storage1", "/path/to/storage2"},
	}
	
	// Create downloader
	downloader, err := goresodownload.NewDownloader(opts)
	if err != nil {
		log.Fatalf("Failed to create downloader: %v", err)
	}
	
	fmt.Printf("\nDownloader created with optimized settings!\n")
	fmt.Printf("Expected performance improvements:\n")
	fmt.Printf("- ~9%% faster file writing (no fsync)\n")
	fmt.Printf("- Reduced directory creation overhead\n")
	fmt.Printf("- Better batch processing performance\n")
	
	// Use the downloader as normal...
	// downloader.ProcessProps(props)
}

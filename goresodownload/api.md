<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# goresodownload

```go
import "github.com/real-rm/goresodownload"
```

## Index

- [Constants](<#constants>)
- [Variables](<#variables>)
- [func CalculatePriority\(boardType string, existMergedProp bson.M\) \(int, error\)](<#CalculatePriority>)
- [func FilterMedias\(medias \[\]bson.M\) \(\[\]bson.M, \[\]bson.M\)](<#FilterMedias>)
- [func GetExistingMergedProperty\(propID string, boardType string\) \(bson.M, error\)](<#GetExistingMergedProperty>)
- [func GetNewPropFromWatchTable\(id, boardType string\) \(bson.M, error\)](<#GetNewPropFromWatchTable>)
- [func GetPropTsForPath\(record bson.M, board string\) \(time.Time, error\)](<#GetPropTsForPath>)
- [type AnalysisResult](<#AnalysisResult>)
- [type CachedFile](<#CachedFile>)
- [type DeleteTask](<#DeleteTask>)
- [type Downloader](<#Downloader>)
  - [func NewDownloader\(opts \*DownloaderOptions\) \(\*Downloader, error\)](<#NewDownloader>)
  - [func \(d \*Downloader\) ProcessAnalysisResult\(result \*AnalysisResult, board string\) \(tnChangedNum int, err error\)](<#Downloader.ProcessAnalysisResult>)
  - [func \(d \*Downloader\) Stop\(\)](<#Downloader.Stop>)
- [type DownloaderConfig](<#DownloaderConfig>)
  - [func NewDefaultConfig\(\) \*DownloaderConfig](<#NewDefaultConfig>)
- [type DownloaderOptions](<#DownloaderOptions>)
- [type FailedTask](<#FailedTask>)
- [type MediaDiffAnalyzer](<#MediaDiffAnalyzer>)
  - [func NewMediaDiffAnalyzer\(\) \*MediaDiffAnalyzer](<#NewMediaDiffAnalyzer>)
  - [func \(a \*MediaDiffAnalyzer\) Analyze\(newProp bson.M, board string\) \(AnalysisResult, error\)](<#MediaDiffAnalyzer.Analyze>)
- [type MediaInfo](<#MediaInfo>)
- [type MediaItemInfo](<#MediaItemInfo>)
- [type MediaTask](<#MediaTask>)
- [type MediaTaskInfo](<#MediaTaskInfo>)
- [type NormalizedProperty](<#NormalizedProperty>)
- [type PropProcessResult](<#PropProcessResult>)
- [type PropProcessor](<#PropProcessor>)
  - [func \(pp \*PropProcessor\) Process\(\) PropProcessResult](<#PropProcessor.Process>)
- [type QueueItem](<#QueueItem>)
- [type ResourceDownloadQueue](<#ResourceDownloadQueue>)
  - [func NewResourceDownloadQueue\(queueCol \*gomongo.MongoCollection\) \(\*ResourceDownloadQueue, error\)](<#NewResourceDownloadQueue>)
  - [func \(q \*ResourceDownloadQueue\) AddToQueue\(id string, priority int, src string\) error](<#ResourceDownloadQueue.AddToQueue>)
  - [func \(q \*ResourceDownloadQueue\) GetNext\(boardType string\) \(\*QueueItem, error\)](<#ResourceDownloadQueue.GetNext>)
  - [func \(q \*ResourceDownloadQueue\) GetNextBatch\(boardType string, batchSize int\) \(\[\]QueueItem, error\)](<#ResourceDownloadQueue.GetNextBatch>)
  - [func \(q \*ResourceDownloadQueue\) RemoveBatchFromQueue\(items \[\]QueueItem\) error](<#ResourceDownloadQueue.RemoveBatchFromQueue>)
  - [func \(q \*ResourceDownloadQueue\) RemoveFromQueue\(item \*QueueItem\) error](<#ResourceDownloadQueue.RemoveFromQueue>)


## Constants

<a name="THUMBNAIL_WIDTH"></a>Download processing constants

```go
const (
    // Thumbnail dimensions
    THUMBNAIL_WIDTH  = 240 // Thumbnail width in pixels
    THUMBNAIL_HEIGHT = 160 // Thumbnail height in pixels

    // Error channel buffer size for concurrent processing
    ErrorChannelBufferSize = 2

    // Memory management constants
    ForceGCThresholdMB = 1000 // Force GC when memory exceeds this threshold

    // File system permissions
    DefaultDirPermissions = 0755 // Standard directory permissions (rwxr-xr-x)
)
```

<a name="DefaultPriority"></a>Priority calculation constants

```go
const (
    // Default priority value used as fallback when calculation fails
    DefaultPriority = 1000

    // Priority bonuses for different property characteristics
    NoPhotoPriorityBonus      = 30000 // Highest priority for properties without photos
    ActiveStatusPriorityBonus = 10000 // High priority for active listings
    CurrentYearPriorityBonus  = 5000  // Bonus for listings from current year
    GTARegionPriorityBonus    = 500   // Bonus for Greater Toronto Area properties
    NewListingPriorityBonus   = 300   // Bonus for new listings (within 3 days)
    ResidentialPriorityBonus  = 200   // Bonus for residential/condo properties

    // Status-specific priority bonuses
    SoldStatusPriorityBonus   = 50 // Bonus for sold properties
    LeasedStatusPriorityBonus = 30 // Bonus for leased properties

    // Geographic priority bonuses
    OntarioPriorityBonus = 20 // Bonus for Ontario province properties

    // Time-based constants
    NewListingTimeWindow = 3 * 24 * time.Hour // 3 days window for new listing bonus
    MaxDaysOnMarketBonus = 30                 // Maximum days on market for bonus calculation

    // Default values for missing data
    DefaultDaysOnMarket = -1 // Default value when days on market is not available
    HoursPerDay         = 24 // Hours in a day for DOM calculation
)
```

<a name="DOWNLOAD_ALLOWED_MS"></a>

```go
const (
    DOWNLOAD_ALLOWED_MS       = 15 * 60 * 1000         // 15 min
    DEFAULT_DOWNLOAD_START_TS = "1970-01-01T00:00:00Z" // Default timestamp for new queue items

    // Batch processing constants
    DefaultBatchSize = 100 // Default number of items to process in a batch
)
```

<a name="MediaCategoryPhoto"></a>

```go
const (
    // Media categories
    MediaCategoryPhoto = "Photo"
)
```

## Variables

<a name="BoardMergedTable"></a>

```go
var BoardMergedTable = map[string]string{
    "CAR": "mls_car_master_records",
    "DDF": "reso_crea_merged",
    "BRE": "bridge_bcre_merged",
    "EDM": "mls_rae_master_records",
    "TRB": "reso_treb_evow_merged",
}
```

<a name="BoardWatchTable"></a>

```go
var BoardWatchTable = map[string]string{
    "CAR": "mls_car_raw_records",
    "DDF": "reso_crea_raw",
    "BRE": "bridge_bcre_raw",
    "EDM": "mls_rae_raw_records",
    "TRB": "reso_treb_evow_merged",
}
```

<a name="CalculatePriority"></a>
## func [CalculatePriority](<https://github.com/real-rm/goresodownload/blob/main/priority_calculator.go#L57>)

```go
func CalculatePriority(boardType string, existMergedProp bson.M) (int, error)
```

CalculatePriority calculates the priority for a property based on the CoffeeScript logic @param boardType \- board type \(TRB, BRE, DDF, EDM, CAR\) @param existMergedProp \- existing merged property record \(can be nil\) @returns priority score and error, higher score = higher priority

<a name="FilterMedias"></a>
## func [FilterMedias](<https://github.com/real-rm/goresodownload/blob/main/media_diff_analyzer.go#L977>)

```go
func FilterMedias(medias []bson.M) ([]bson.M, []bson.M)
```

FilterMedias filters medias by status, permission, and statement input: medias is a list of media output: a list of filtered photo medias, a list of filtered document medias

<a name="GetExistingMergedProperty"></a>
## func [GetExistingMergedProperty](<https://github.com/real-rm/goresodownload/blob/main/priority_calculator.go#L260>)

```go
func GetExistingMergedProperty(propID string, boardType string) (bson.M, error)
```

GetExistingMergedProperty fetches the existing merged property from database

<a name="GetNewPropFromWatchTable"></a>
## func [GetNewPropFromWatchTable](<https://github.com/real-rm/goresodownload/blob/main/resource_download_queue.go#L111>)

```go
func GetNewPropFromWatchTable(id, boardType string) (bson.M, error)
```

GetNewPropFromWatchTable retrieves the full document from the appropriate watch table

<a name="GetPropTsForPath"></a>
## func [GetPropTsForPath](<https://github.com/real-rm/goresodownload/blob/main/utils.go#L14>)

```go
func GetPropTsForPath(record bson.M, board string) (time.Time, error)
```

GetPropTsForPath 根据board类型和记录内容获取用于路径计算的时间戳 TRB board优先级: ListingContractDate \> ModificationTimestamp \> OriginalEntryTimestamp \> ts 非TRB board优先级: ListingContractDate \> ts

<a name="AnalysisResult"></a>
## type [AnalysisResult](<https://github.com/real-rm/goresodownload/blob/main/media_diff_analyzer.go#L165-L181>)

AnalysisResult represents the result of media analysis

```go
type AnalysisResult struct {
    ID            string
    Sid           string
    DownloadTasks []MediaTask
    DeleteTasks   []DeleteTask
    PhoLH         []int32  // photo hash list
    DocLH         []string // document hash list
    PropTs        time.Time
    OldTnLH       int32
    NewFirstPic   MediaTask
    // 图片下载统计字段
    MaxPicSz int64     // 下载的图片当中最大size
    AvgPicSz int64     // 所有图片平均size
    TotPicSz int64     // 总图片size
    PhodlNum int       // 下载图片数量
    PhoDl    time.Time // 下载完成时间戳
}
```

<a name="CachedFile"></a>
## type [CachedFile](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L39-L44>)

CachedFile represents a file cached in memory

```go
type CachedFile struct {
    Path        string // Relative path where the file should be saved
    Data        []byte // File data
    IsPhoto     bool   // Whether this is a photo file
    IsThumbnail bool   // Whether this is a thumbnail file
}
```

<a name="DeleteTask"></a>
## type [DeleteTask](<https://github.com/real-rm/goresodownload/blob/main/media_diff_analyzer.go#L136-L140>)

DeleteTask represents a file deletion task

```go
type DeleteTask struct {
    Sid      string
    MediaKey string
    Path     string
}
```

<a name="Downloader"></a>
## type [Downloader](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L111-L113>)

Downloader implements the media download functionality

```go
type Downloader struct {
    *DownloaderOptions
}
```

<a name="NewDownloader"></a>
### func [NewDownloader](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L116>)

```go
func NewDownloader(opts *DownloaderOptions) (*Downloader, error)
```

NewDownloader creates a new Downloader instance with worker pool

<a name="Downloader.ProcessAnalysisResult"></a>
### func \(\*Downloader\) [ProcessAnalysisResult](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L140>)

```go
func (d *Downloader) ProcessAnalysisResult(result *AnalysisResult, board string) (tnChangedNum int, err error)
```

ProcessAnalysisResult processes the analysis result directly using PropProcessor

<a name="Downloader.Stop"></a>
### func \(\*Downloader\) [Stop](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L135>)

```go
func (d *Downloader) Stop()
```

Stop gracefully shuts down the downloader

<a name="DownloaderConfig"></a>
## type [DownloaderConfig](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L78-L90>)

DownloaderConfig represents the configuration for the downloader

```go
type DownloaderConfig struct {
    // Concurrency settings
    PropConcurrency int `json:"prop_concurrency"` // Number of props to process concurrently

    // Retry settings
    MaxRetries int `json:"max_retries"`

    // Alert settings
    ConsecutiveFailuresThreshold int `json:"consecutive_failures_threshold"` // Default: 3

    // Performance settings
    DisableSync bool `json:"disable_sync"` // Disable fsync for better performance (default: false)
}
```

<a name="NewDefaultConfig"></a>
### func [NewDefaultConfig](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L93>)

```go
func NewDefaultConfig() *DownloaderConfig
```

NewDefaultConfig returns a new DownloaderConfig with default values

<a name="DownloaderOptions"></a>
## type [DownloaderOptions](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L103-L108>)

DownloaderOptions contains all options for creating a Downloader

```go
type DownloaderOptions struct {
    Config       *DownloaderConfig
    StoragePaths []string
    MergedCol    *gomongo.MongoCollection
    FailedCol    *gomongo.MongoCollection
}
```

<a name="FailedTask"></a>
## type [FailedTask](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L69-L75>)

FailedTask represents a failed download task in the database

```go
type FailedTask struct {
    ID         primitive.ObjectID `bson:"_id,omitempty"`
    PropID     string             `bson:"prop_id"`
    ImageURL   string             `bson:"image_url"`
    ErrMsg     string             `bson:"err_msg"`
    RetryCount int                `bson:"retry_count"`
}
```

<a name="MediaDiffAnalyzer"></a>
## type [MediaDiffAnalyzer](<https://github.com/real-rm/goresodownload/blob/main/media_diff_analyzer.go#L193-L194>)

MediaDiffAnalyzer analyzes differences between old and new media lists

```go
type MediaDiffAnalyzer struct {
}
```

<a name="NewMediaDiffAnalyzer"></a>
### func [NewMediaDiffAnalyzer](<https://github.com/real-rm/goresodownload/blob/main/media_diff_analyzer.go#L197>)

```go
func NewMediaDiffAnalyzer() *MediaDiffAnalyzer
```

NewMediaDiffAnalyzer creates a new MediaDiffAnalyzer instance

<a name="MediaDiffAnalyzer.Analyze"></a>
### func \(\*MediaDiffAnalyzer\) [Analyze](<https://github.com/real-rm/goresodownload/blob/main/media_diff_analyzer.go#L231>)

```go
func (a *MediaDiffAnalyzer) Analyze(newProp bson.M, board string) (AnalysisResult, error)
```

Analyze compares old and new media lists and generates download and delete tasks

<a name="MediaInfo"></a>
## type [MediaInfo](<https://github.com/real-rm/goresodownload/blob/main/media_diff_analyzer.go#L143-L152>)

MediaInfo represents media information extracted from change document

```go
type MediaInfo struct {
    ID           string
    Sid          string
    NewMediaList []bson.M
    Timestamp    time.Time
    OldPhoLHList []int32
    OldDocLHList []string
    OldTnLH      int32
    NewFirstPic  MediaTask
}
```

<a name="MediaItemInfo"></a>
## type [MediaItemInfo](<https://github.com/real-rm/goresodownload/blob/main/media_diff_analyzer.go#L184-L190>)

MediaItemInfo represents extracted information from a media item

```go
type MediaItemInfo struct {
    MediaKey string
    URL      string
    Type     string
    IsPhoto  bool
    Category string
}
```

<a name="MediaTask"></a>
## type [MediaTask](<https://github.com/real-rm/goresodownload/blob/main/media_diff_analyzer.go#L124-L133>)

MediaTask represents a download task

```go
type MediaTask struct {
    // TaskID   string
    Sid      string
    MediaKey string
    URL      string
    Type     string // photo/pdf/zip
    DestPath string
    IsPhoto  bool
    OldTnLH  int32
}
```

<a name="MediaTaskInfo"></a>
## type [MediaTaskInfo](<https://github.com/real-rm/goresodownload/blob/main/media_diff_analyzer.go#L155-L162>)

MediaTaskInfo represents information needed to generate media tasks

```go
type MediaTaskInfo struct {
    Sid          string
    NewMedia     []bson.M
    OldPhoLHList []int32
    OldDocLHList []string
    Timestamp    time.Time
    Board        string
}
```

<a name="NormalizedProperty"></a>
## type [NormalizedProperty](<https://github.com/real-rm/goresodownload/blob/main/priority_calculator.go#L44-L51>)

NormalizedProperty holds normalized property data across different boards

```go
type NormalizedProperty struct {
    MlsStatus    string
    OnD          *time.Time // ListingContractDate or OriginalEntryTimestamp
    Region       string     // CountyOrParish, CityRegion, StateRegion
    PropertyType string
    Province     string // StateOrProvince
    DOM          int    // DaysOnMarket or calculated
}
```

<a name="PropProcessResult"></a>
## type [PropProcessResult](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L47-L52>)

PropProcessResult represents the result of processing a single prop

```go
type PropProcessResult struct {
    PropID        string
    ThumbnailHash int32
    TnChangedNum  int
    Error         error
}
```

<a name="PropProcessor"></a>
## type [PropProcessor](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L61-L66>)

PropProcessor handles processing of a single prop's media tasks

```go
type PropProcessor struct {
    // contains filtered or unexported fields
}
```

<a name="PropProcessor.Process"></a>
### func \(\*PropProcessor\) [Process](<https://github.com/real-rm/goresodownload/blob/main/downloader.go#L407>)

```go
func (pp *PropProcessor) Process() PropProcessResult
```

Process handles all tasks for a single prop

<a name="QueueItem"></a>
## type [QueueItem](<https://github.com/real-rm/goresodownload/blob/main/resource_download_queue.go#L23-L29>)

QueueItem represents an item in the download queue

```go
type QueueItem struct {
    ID           string    `bson:"_id"`
    Src          string    `bson:"src"`
    Mt           time.Time `bson:"_mt"`
    DlShallEndTs time.Time `bson:"dlShallEndTs"`
    Priority     int       `bson:"priority"`
}
```

<a name="ResourceDownloadQueue"></a>
## type [ResourceDownloadQueue](<https://github.com/real-rm/goresodownload/blob/main/resource_download_queue.go#L32-L34>)

ResourceDownloadQueue manages the download queue for resources

```go
type ResourceDownloadQueue struct {
    // contains filtered or unexported fields
}
```

<a name="NewResourceDownloadQueue"></a>
### func [NewResourceDownloadQueue](<https://github.com/real-rm/goresodownload/blob/main/resource_download_queue.go#L37>)

```go
func NewResourceDownloadQueue(queueCol *gomongo.MongoCollection) (*ResourceDownloadQueue, error)
```

NewResourceDownloadQueue creates a new ResourceDownloadQueue instance

<a name="ResourceDownloadQueue.AddToQueue"></a>
### func \(\*ResourceDownloadQueue\) [AddToQueue](<https://github.com/real-rm/goresodownload/blob/main/resource_download_queue.go#L80>)

```go
func (q *ResourceDownloadQueue) AddToQueue(id string, priority int, src string) error
```

AddToQueue adds a resource to the download queue

<a name="ResourceDownloadQueue.GetNext"></a>
### func \(\*ResourceDownloadQueue\) [GetNext](<https://github.com/real-rm/goresodownload/blob/main/resource_download_queue.go#L156>)

```go
func (q *ResourceDownloadQueue) GetNext(boardType string) (*QueueItem, error)
```

GetNext gets the next single resource to download for a specific board Uses findOneAndUpdate to atomically find and lock the next item

<a name="ResourceDownloadQueue.GetNextBatch"></a>
### func \(\*ResourceDownloadQueue\) [GetNextBatch](<https://github.com/real-rm/goresodownload/blob/main/resource_download_queue.go#L130>)

```go
func (q *ResourceDownloadQueue) GetNextBatch(boardType string, batchSize int) ([]QueueItem, error)
```

GetNextBatch gets the next batch of resources to download for a specific board Loops batchSize times calling GetNext to get individual items

<a name="ResourceDownloadQueue.RemoveBatchFromQueue"></a>
### func \(\*ResourceDownloadQueue\) [RemoveBatchFromQueue](<https://github.com/real-rm/goresodownload/blob/main/resource_download_queue.go#L204>)

```go
func (q *ResourceDownloadQueue) RemoveBatchFromQueue(items []QueueItem) error
```

RemoveBatchFromQueue removes multiple resources from the queue

<a name="ResourceDownloadQueue.RemoveFromQueue"></a>
### func \(\*ResourceDownloadQueue\) [RemoveFromQueue](<https://github.com/real-rm/goresodownload/blob/main/resource_download_queue.go#L191>)

```go
func (q *ResourceDownloadQueue) RemoveFromQueue(item *QueueItem) error
```

RemoveFromQueue removes a resource from the queue

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
